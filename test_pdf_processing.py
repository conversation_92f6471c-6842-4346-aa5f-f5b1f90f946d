#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF处理功能测试脚本
"""

import fitz
import os

def create_test_pdf():
    """创建一个测试用的大尺寸PDF文件"""
    # 创建一个超大尺寸的PDF（模拟问题PDF）
    doc = fitz.open()
    
    # 创建一个很大的页面 (2000 x 3000 points, 远超A4的595x842)
    page = doc.new_page(width=2000, height=3000)
    
    # 添加一些文本内容
    text = """这是一个测试PDF文件
    
此PDF文件的页面尺寸超过了标准范围，
在Adobe Acrobat中可能会显示：
"此页面的尺寸超过范围，页面内容可能会被截断"

页面尺寸: 2000 x 3000 points
标准A4: 595 x 842 points

这个文件用于测试PDF修复工具的功能。
"""
    
    # 在页面上添加文本
    page.insert_text((50, 100), text, fontsize=16, color=(0, 0, 0))
    
    # 添加一些图形元素
    rect = fitz.Rect(100, 200, 500, 400)
    page.draw_rect(rect, color=(1, 0, 0), width=2)
    page.insert_text((120, 250), "红色矩形", fontsize=12, color=(1, 0, 0))
    
    # 在页面底部添加更多内容
    page.insert_text((50, 2800), "页面底部内容 - 测试是否会被截断", fontsize=14, color=(0, 0, 1))
    
    # 保存测试文件
    test_file = "test_large_pdf.pdf"
    doc.save(test_file)
    doc.close()
    
    print(f"已创建测试PDF文件: {test_file}")
    print(f"文件大小: {os.path.getsize(test_file)} bytes")
    return test_file

def test_pdf_fix(input_file):
    """测试PDF修复功能"""
    try:
        # 打开原始PDF
        doc = fitz.open(input_file)
        print(f"\n原始PDF信息:")
        print(f"页数: {len(doc)}")
        
        if len(doc) > 0:
            page = doc[0]
            rect = page.rect
            print(f"第一页尺寸: {rect.width} x {rect.height} points")
            print(f"是否超过A4尺寸: {rect.width > 595 or rect.height > 842}")
        
        # 模拟修复过程
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            original_rect = page.rect
            
            # 计算缩放到A4的比例
            target_width = 595
            target_height = 842
            
            scale_x = target_width / original_rect.width
            scale_y = target_height / original_rect.height
            scale = min(scale_x, scale_y)  # 保持宽高比
            
            new_width = original_rect.width * scale
            new_height = original_rect.height * scale
            
            print(f"\n修复信息:")
            print(f"缩放比例: {scale:.3f}")
            print(f"新尺寸: {new_width:.1f} x {new_height:.1f} points")
            
            # 创建新页面
            new_page = new_doc.new_page(width=new_width, height=new_height)

            # 将原页面内容复制到新页面（自动缩放）
            new_page.show_pdf_page(new_page.rect, doc, page_num, keep_proportion=True)
        
        # 保存修复后的文件
        output_file = "test_large_pdf_fixed.pdf"
        new_doc.save(output_file, garbage=4, deflate=True, clean=True)
        new_doc.close()
        doc.close()
        
        print(f"\n已创建修复后的PDF: {output_file}")
        print(f"修复后文件大小: {os.path.getsize(output_file)} bytes")
        
        # 验证修复后的文件
        fixed_doc = fitz.open(output_file)
        if len(fixed_doc) > 0:
            fixed_page = fixed_doc[0]
            fixed_rect = fixed_page.rect
            print(f"修复后第一页尺寸: {fixed_rect.width:.1f} x {fixed_rect.height:.1f} points")
            print(f"是否适合A4: {fixed_rect.width <= 595 and fixed_rect.height <= 842}")
        fixed_doc.close()
        
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("PDF处理功能测试")
    print("=" * 50)
    
    # 创建测试PDF
    test_file = create_test_pdf()
    
    # 测试修复功能
    success = test_pdf_fix(test_file)
    
    if success:
        print("\n✅ 测试成功！PDF修复功能正常工作")
        print("\n你可以用以下软件打开测试文件进行验证:")
        print("1. test_large_pdf.pdf - 原始大尺寸文件（在Acrobat中可能显示异常）")
        print("2. test_large_pdf_fixed.pdf - 修复后文件（应该在Acrobat中正常显示）")
    else:
        print("\n❌ 测试失败！请检查错误信息")

if __name__ == "__main__":
    main()
