#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF修复工具 - 解决Acrobat中PDF页面尺寸超出范围的问题
通过重新缩放PDF页面使其适合标准纸张大小
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
from pathlib import Path
import threading
import fitz  # PyMuPDF
from typing import List, Tuple

class PDFFixerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF修复工具 - 解决Acrobat显示问题")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 文件列表
        self.pdf_files = []
        self.output_folder = ""
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="PDF修复工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="选择PDF文件", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Button(file_frame, text="选择文件", command=self.select_files).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(file_frame, text="清空列表", command=self.clear_files).grid(row=0, column=2, padx=(10, 0))
        
        # 文件列表
        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview来显示文件列表
        self.file_tree = ttk.Treeview(list_frame, columns=("path", "size", "status"), show="headings", height=10)
        self.file_tree.heading("path", text="文件路径")
        self.file_tree.heading("size", text="文件大小")
        self.file_tree.heading("status", text="状态")
        
        self.file_tree.column("path", width=400)
        self.file_tree.column("size", width=100)
        self.file_tree.column("status", width=100)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        self.file_tree.configure(yscrollcommand=scrollbar.set)
        
        self.file_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 输出设置区域
        output_frame = ttk.LabelFrame(main_frame, text="输出设置", padding="10")
        output_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        output_frame.columnconfigure(1, weight=1)
        
        ttk.Label(output_frame, text="输出文件夹:").grid(row=0, column=0, padx=(0, 10))
        self.output_var = tk.StringVar(value="与原文件相同位置")
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_var, state="readonly")
        self.output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(output_frame, text="选择文件夹", command=self.select_output_folder).grid(row=0, column=2)
        
        # 处理选项
        options_frame = ttk.LabelFrame(main_frame, text="处理选项", padding="10")
        options_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        options_frame.columnconfigure(1, weight=1)

        # 缩放模式
        ttk.Label(options_frame, text="缩放模式:").grid(row=0, column=0, sticky=tk.W)
        self.scale_var = tk.StringVar(value="fit_page")
        scale_frame = ttk.Frame(options_frame)
        scale_frame.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Radiobutton(scale_frame, text="适合页面大小", variable=self.scale_var, value="fit_page").grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(scale_frame, text="适合A4纸张", variable=self.scale_var, value="fit_a4").grid(row=0, column=1, sticky=tk.W, padx=(20, 0))

        # 处理方法
        ttk.Label(options_frame, text="处理方法:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.method_var = tk.StringVar(value="smart")
        method_frame = ttk.Frame(options_frame)
        method_frame.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        ttk.Radiobutton(method_frame, text="智能修复(推荐)", variable=self.method_var, value="smart").grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(method_frame, text="矢量复制", variable=self.method_var, value="vector").grid(row=0, column=1, sticky=tk.W, padx=(15, 0))
        ttk.Radiobutton(method_frame, text="图像渲染", variable=self.method_var, value="image").grid(row=0, column=2, sticky=tk.W, padx=(15, 0))

        # 图像质量设置
        quality_frame = ttk.Frame(options_frame)
        quality_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        ttk.Label(quality_frame, text="图像质量(DPI):").grid(row=0, column=0, sticky=tk.W)
        self.dpi_var = tk.StringVar(value="300")
        dpi_combo = ttk.Combobox(quality_frame, textvariable=self.dpi_var, values=["150", "200", "300", "400", "600"], width=10, state="readonly")
        dpi_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Label(quality_frame, text="(仅图像渲染模式)").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))

        # 文件名后缀
        ttk.Label(options_frame, text="文件名后缀:").grid(row=3, column=0, sticky=tk.W, pady=(10, 0))
        self.suffix_var = tk.StringVar(value="_fixed")
        ttk.Entry(options_frame, textvariable=self.suffix_var, width=15).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        # 进度条和控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(0, weight=1)
        
        self.progress = ttk.Progressbar(control_frame, mode='determinate')
        self.progress.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(control_frame, textvariable=self.status_var)
        self.status_label.grid(row=1, column=0, sticky=tk.W)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=3)
        
        self.process_button = ttk.Button(button_frame, text="开始处理", command=self.start_processing)
        self.process_button.grid(row=0, column=0, padx=(0, 10))
        
        ttk.Button(button_frame, text="退出", command=self.root.quit).grid(row=0, column=1)
        
    def select_files(self):
        """选择PDF文件"""
        files = filedialog.askopenfilenames(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        for file_path in files:
            if file_path not in [item[0] for item in self.pdf_files]:
                file_size = self.get_file_size(file_path)
                self.pdf_files.append((file_path, file_size, "待处理"))
                
                # 添加到树形视图
                self.file_tree.insert("", "end", values=(file_path, file_size, "待处理"))
        
        self.status_var.set(f"已选择 {len(self.pdf_files)} 个文件")
    
    def clear_files(self):
        """清空文件列表"""
        self.pdf_files.clear()
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)
        self.status_var.set("文件列表已清空")
    
    def select_output_folder(self):
        """选择输出文件夹"""
        folder = filedialog.askdirectory(title="选择输出文件夹")
        if folder:
            self.output_folder = folder
            self.output_var.set(folder)
    
    def get_file_size(self, file_path: str) -> str:
        """获取文件大小"""
        try:
            size = os.path.getsize(file_path)
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        except:
            return "未知"
    
    def start_processing(self):
        """开始处理PDF文件"""
        if not self.pdf_files:
            messagebox.showwarning("警告", "请先选择PDF文件")
            return
        
        # 在新线程中处理，避免界面冻结
        self.process_button.config(state="disabled")
        thread = threading.Thread(target=self.process_files)
        thread.daemon = True
        thread.start()
    
    def process_files(self):
        """处理PDF文件的主要逻辑"""
        total_files = len(self.pdf_files)
        self.progress.config(maximum=total_files)
        
        success_count = 0
        error_count = 0
        
        for i, (file_path, _, _) in enumerate(self.pdf_files):
            try:
                # 更新状态
                self.root.after(0, lambda i=i: self.update_file_status(i, "处理中..."))
                self.root.after(0, lambda: self.status_var.set(f"正在处理: {os.path.basename(file_path)}"))
                
                # 处理PDF文件
                success = self.fix_pdf(file_path)
                
                if success:
                    success_count += 1
                    self.root.after(0, lambda i=i: self.update_file_status(i, "完成"))
                else:
                    error_count += 1
                    self.root.after(0, lambda i=i: self.update_file_status(i, "失败"))
                
            except Exception as e:
                error_count += 1
                self.root.after(0, lambda i=i: self.update_file_status(i, f"错误: {str(e)[:20]}"))
            
            # 更新进度条
            self.root.after(0, lambda: self.progress.config(value=i + 1))
        
        # 处理完成
        self.root.after(0, lambda: self.processing_complete(success_count, error_count))
    
    def update_file_status(self, index: int, status: str):
        """更新文件状态"""
        items = self.file_tree.get_children()
        if index < len(items):
            item = items[index]
            values = list(self.file_tree.item(item, "values"))
            values[2] = status
            self.file_tree.item(item, values=values)
    
    def processing_complete(self, success_count: int, error_count: int):
        """处理完成"""
        self.process_button.config(state="normal")
        self.status_var.set(f"处理完成: 成功 {success_count} 个，失败 {error_count} 个")
        
        if error_count == 0:
            messagebox.showinfo("完成", f"所有 {success_count} 个文件处理成功！")
        else:
            messagebox.showwarning("完成", f"处理完成：成功 {success_count} 个，失败 {error_count} 个")

    def fix_pdf(self, input_path: str) -> bool:
        """修复PDF文件 - 核心处理逻辑"""
        try:
            # 确定输出路径
            if self.output_folder:
                output_dir = self.output_folder
            else:
                output_dir = os.path.dirname(input_path)

            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            suffix = self.suffix_var.get()
            output_path = os.path.join(output_dir, f"{base_name}{suffix}.pdf")

            # 打开PDF文档
            doc = fitz.open(input_path)

            # 创建新的PDF文档
            new_doc = fitz.open()

            # 定义标准页面尺寸 (A4: 595 x 842 points)
            if self.scale_var.get() == "fit_a4":
                target_width = 595
                target_height = 842
            else:
                # 使用第一页作为参考尺寸
                if len(doc) > 0:
                    first_page = doc[0]
                    rect = first_page.rect
                    target_width = min(rect.width, 595)  # 不超过A4宽度
                    target_height = min(rect.height, 842)  # 不超过A4高度
                else:
                    target_width = 595
                    target_height = 842

            # 处理每一页
            for page_num in range(len(doc)):
                page = doc[page_num]

                # 获取原始页面尺寸
                original_rect = page.rect
                original_width = original_rect.width
                original_height = original_rect.height

                # 计算缩放比例
                scale_x = target_width / original_width
                scale_y = target_height / original_height
                scale = min(scale_x, scale_y)  # 保持宽高比

                # 计算新的页面尺寸
                new_width = original_width * scale
                new_height = original_height * scale

                # 创建新页面
                new_page = new_doc.new_page(width=new_width, height=new_height)

                # 根据选择的处理方法复制页面内容
                method = self.method_var.get()

                if method == "smart":
                    # 智能修复方法 - 多种方法自动选择
                    success = self._smart_fix_page(page, new_page, scale, doc, page_num)
                    if not success:
                        print(f"智能修复失败，使用图像方法，页面 {page_num + 1}")
                        self._render_page_as_image(page, new_page, scale)

                elif method == "vector":
                    # 矢量复制方法 - 保持高质量
                    try:
                        new_page.show_pdf_page(new_page.rect, doc, page_num, keep_proportion=False)
                    except Exception:
                        try:
                            new_page.show_pdf_page(new_page.rect, doc, page_num, keep_proportion=True)
                        except Exception:
                            print(f"矢量复制失败，降级到图像方法，页面 {page_num + 1}")
                            self._render_page_as_image(page, new_page, scale)
                else:
                    # 图像渲染方法 - 兼容性好
                    self._render_page_as_image(page, new_page, scale)

            # 根据处理方法选择保存参数
            method = self.method_var.get()
            if method == "smart":
                # 智能模式：生成兼容性最佳的PDF
                new_doc.save(output_path, garbage=4, deflate=True, clean=True, pretty=True)
            elif method == "vector":
                # 矢量模式：保持原始质量
                new_doc.save(output_path, garbage=3, deflate=True, clean=False, pretty=True)
            else:
                # 图像模式：优化文件大小
                new_doc.save(output_path, garbage=4, deflate=True, clean=True)

            new_doc.close()
            doc.close()

            return True

        except Exception as e:
            print(f"处理文件 {input_path} 时出错: {str(e)}")
            return False

    def _render_page_as_image(self, page, new_page, scale):
        """将页面渲染为图像并插入到新页面"""
        try:
            # 获取DPI设置
            dpi = int(self.dpi_var.get())

            # 计算渲染矩阵
            # DPI转换：72 DPI是PDF的标准，所以缩放因子 = dpi/72
            render_scale = dpi / 72.0
            mat = fitz.Matrix(scale * render_scale, scale * render_scale)

            # 渲染页面为高质量图像
            pix = page.get_pixmap(matrix=mat, alpha=False)

            # 插入图像到新页面
            new_page.insert_image(new_page.rect, pixmap=pix)

            # 释放内存
            pix = None

        except Exception as e:
            print(f"图像渲染失败: {str(e)}")
            # 降级到最基本的方法
            try:
                new_page.show_pdf_page(new_page.rect, page.parent, page.number)
            except:
                pass

    def _smart_fix_page(self, page, new_page, scale, doc, page_num):
        """智能修复页面 - 尝试多种方法自动选择最佳方案"""

        # 方法1: 尝试矢量复制（最佳质量）
        try:
            new_page.show_pdf_page(new_page.rect, doc, page_num, keep_proportion=False)
            return True
        except Exception:
            pass

        # 方法2: 尝试保持比例的矢量复制
        try:
            new_page.show_pdf_page(new_page.rect, doc, page_num, keep_proportion=True)
            return True
        except Exception:
            pass

        # 方法3: 使用高质量图像渲染
        try:
            # 使用较高的DPI进行渲染
            dpi = int(self.dpi_var.get())
            render_scale = max(2.0, dpi / 150.0)  # 至少2倍缩放
            mat = fitz.Matrix(scale * render_scale, scale * render_scale)
            pix = page.get_pixmap(matrix=mat, alpha=False)

            # 插入图像
            new_page.insert_image(new_page.rect, pixmap=pix)
            pix = None
            return True

        except Exception:
            pass

        # 方法4: 最基本的图像渲染
        try:
            mat = fitz.Matrix(scale, scale)
            pix = page.get_pixmap(matrix=mat, alpha=False)
            new_page.insert_image(new_page.rect, pixmap=pix)
            pix = None
            return True
        except Exception:
            pass

        return False

    def _int_to_rgb(self, color_int):
        """将整数颜色转换为RGB元组"""
        try:
            if color_int == 0:
                return (0, 0, 0)  # 黑色

            # 转换为RGB
            r = (color_int >> 16) & 0xFF
            g = (color_int >> 8) & 0xFF
            b = color_int & 0xFF

            return (r/255.0, g/255.0, b/255.0)
        except:
            return (0, 0, 0)  # 默认黑色


def main():
    """主函数"""
    # 检查依赖
    try:
        import fitz
    except ImportError:
        messagebox.showerror("错误", "缺少必要的依赖库 PyMuPDF。\n请运行: pip install PyMuPDF")
        return

    # 创建主窗口
    root = tk.Tk()
    app = PDFFixerGUI(root)

    # 设置窗口图标（如果有的话）
    try:
        # 可以在这里设置应用程序图标
        pass
    except:
        pass

    # 运行应用程序
    root.mainloop()


if __name__ == "__main__":
    main()
