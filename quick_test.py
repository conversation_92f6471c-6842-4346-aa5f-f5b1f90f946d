#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试新的PDF处理功能
"""

import fitz
import os

def test_vector_method():
    """测试矢量复制方法"""
    print("测试矢量复制方法...")
    
    # 打开测试文件
    if not os.path.exists("test_large_pdf.pdf"):
        print("❌ 测试文件不存在，请先运行 test_pdf_processing.py")
        return False
    
    try:
        doc = fitz.open("test_large_pdf.pdf")
        new_doc = fitz.open()
        
        # 处理第一页
        page = doc[0]
        original_rect = page.rect
        
        # 缩放到A4
        scale = min(595 / original_rect.width, 842 / original_rect.height)
        new_width = original_rect.width * scale
        new_height = original_rect.height * scale
        
        new_page = new_doc.new_page(width=new_width, height=new_height)
        
        # 矢量复制
        new_page.show_pdf_page(new_page.rect, doc, 0, keep_proportion=False)
        
        # 保存
        output_file = "test_vector_method.pdf"
        new_doc.save(output_file, garbage=3, deflate=True, clean=False, pretty=True)
        new_doc.close()
        doc.close()
        
        # 检查结果
        file_size = os.path.getsize(output_file)
        print(f"✅ 矢量方法测试成功")
        print(f"   输出文件: {output_file}")
        print(f"   文件大小: {file_size} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ 矢量方法测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("PDF处理方法测试")
    print("=" * 40)
    
    # 测试矢量方法
    vector_success = test_vector_method()
    
    if vector_success:
        print("\n🎉 测试完成！")
        print("\n现在你可以使用改进后的程序:")
        print("1. 运行 python pdf_fixer.py")
        print("2. 选择'矢量复制(高质量)'模式")
        print("3. 这样可以保持文件大小和质量")
    else:
        print("\n⚠️ 测试未完全成功，但程序仍可使用")

if __name__ == "__main__":
    main()
