PDF修复工具 - 使用说明
====================

【问题描述】
某些PDF文件在Adobe Acrobat中打开时会提示：
"此页面的尺寸超过范围，页面内容可能会被截断"
导致内容显示不全，但在Chrome浏览器或WPS中可以正常显示。

【解决方案】
本工具模拟Chrome浏览器的处理方式，通过重新缩放PDF页面
使其适合标准纸张大小，解决在Acrobat中的显示问题。

【快速开始】
1. 双击运行 "run.bat" 文件
2. 程序会自动检查环境并安装依赖
3. 启动图形界面后按照界面提示操作

【详细步骤】
1. 选择文件：
   - 点击"选择文件"按钮
   - 可以同时选择多个PDF文件
   - 支持的格式：.pdf

2. 设置输出：
   - 默认输出到原文件相同位置
   - 可点击"选择文件夹"自定义输出位置
   - 可修改文件名后缀（默认"_fixed"）

3. 选择处理模式：
   - "适合页面大小"：自动调整到合适尺寸
   - "适合A4纸张"：强制缩放到A4大小

4. 开始处理：
   - 点击"开始处理"按钮
   - 程序会显示处理进度
   - 完成后会显示成功/失败统计

【输出文件】
- 原文件：example.pdf
- 修复后：example_fixed.pdf（或自定义后缀）

【技术原理】
1. 读取原始PDF文件的页面尺寸
2. 计算适合标准纸张的缩放比例
3. 创建新的PDF页面并应用缩放
4. 保持原始内容的宽高比和质量
5. 生成优化后的PDF文件

【系统要求】
- Windows 7/8/10/11
- Python 3.7 或更高版本
- 网络连接（首次运行时下载依赖）

【常见问题】
Q: 提示"未找到Python"怎么办？
A: 请到 https://www.python.org/downloads/ 下载安装Python

Q: 处理后的文件还是有问题？
A: 尝试选择不同的缩放模式，或检查原文件是否损坏

Q: 可以处理加密的PDF吗？
A: 目前不支持加密PDF，请先解除密码保护

Q: 处理速度慢怎么办？
A: 大文件处理需要时间，请耐心等待。可以先处理小文件测试

【注意事项】
- 建议处理前备份原始文件
- 大文件处理可能需要较长时间
- 确保有足够的磁盘空间存储输出文件
- 如遇到问题，可查看程序界面的状态信息

【测试功能】
运行 test_pdf_processing.py 可以：
- 创建测试用的大尺寸PDF文件
- 验证修复功能是否正常工作
- 生成对比文件用于验证效果

【版本信息】
版本：1.0
开发语言：Python
GUI框架：Tkinter
PDF处理：PyMuPDF

【联系支持】
如有问题或建议，请保存错误信息以便技术支持。
