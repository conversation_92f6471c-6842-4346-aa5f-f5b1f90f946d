@echo off
chcp 65001 >nul
title PDF修复工具
echo ========================================
echo           PDF修复工具 v1.0
echo     解决Acrobat页面尺寸超出范围问题
echo ========================================
echo.

REM 检查Python是否安装
echo [1/3] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python 3.7或更高版本
    echo    下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo ✅ Python版本: %%i
)

REM 检查依赖是否安装
echo [2/3] 检查依赖库...
python -c "import fitz" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  正在安装依赖库PyMuPDF...
    pip install PyMuPDF
    if errorlevel 1 (
        echo ❌ 依赖安装失败，请手动运行: pip install PyMuPDF
        pause
        exit /b 1
    ) else (
        echo ✅ PyMuPDF安装成功
    )
) else (
    echo ✅ PyMuPDF已安装
)

REM 运行程序
echo [3/3] 启动程序...
echo.
python pdf_fixer.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错，请检查错误信息
    pause
)
