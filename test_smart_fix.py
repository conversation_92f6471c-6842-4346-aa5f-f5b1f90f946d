#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能修复功能
"""

import fitz
import os

def test_smart_fix():
    """测试智能修复方法"""
    print("🔧 测试智能修复方法...")
    
    # 检查是否有测试文件
    test_files = [
        "翰思艾泰文化墙设计方案20250724.pdf",
        "翰思艾泰文化墙设计方案20250724_fixed.pdf"
    ]
    
    found_files = []
    for file in test_files:
        if os.path.exists(file):
            found_files.append(file)
            size = os.path.getsize(file)
            print(f"📄 找到文件: {file} ({size} bytes)")
    
    if not found_files:
        print("❌ 未找到测试文件")
        return False
    
    # 测试基本的PDF操作
    try:
        test_file = found_files[0]
        doc = fitz.open(test_file)
        print(f"✅ 成功打开PDF: {len(doc)} 页")
        
        # 测试第一页
        page = doc[0]
        rect = page.rect
        print(f"📐 页面尺寸: {rect.width} x {rect.height}")
        
        # 测试渲染
        pix = page.get_pixmap(matrix=fitz.Matrix(1, 1))
        print(f"🖼️ 渲染测试成功: {pix.width} x {pix.height}")
        
        doc.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    print("智能修复功能测试")
    print("=" * 40)
    
    success = test_smart_fix()
    
    if success:
        print("\n🎉 测试通过！")
        print("\n现在可以使用智能修复模式:")
        print("1. 运行: python pdf_fixer.py")
        print("2. 选择'智能修复(推荐)'")
        print("3. 程序会自动选择最佳处理方法")
    else:
        print("\n⚠️ 测试未完全通过，但程序仍可使用")

if __name__ == "__main__":
    main()
