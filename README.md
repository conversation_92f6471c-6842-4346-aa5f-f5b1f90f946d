# PDF修复工具

这是一个Python GUI应用程序，用于解决PDF文件在Adobe Acrobat中显示"此页面的尺寸超过范围，页面内容可能会被截断"的问题。

## 问题描述

某些PDF文件在Adobe Acrobat中打开时会提示页面尺寸超出范围，导致内容显示不全。但是这些文件在Chrome浏览器或WPS等软件中可以正常显示。

## 解决方案

本工具模拟Chrome浏览器的处理方式，通过重新缩放PDF页面使其适合标准纸张大小，从而解决在Acrobat中的显示问题。

## 功能特点

- 🖥️ 友好的图形用户界面
- 📁 支持批量选择和处理多个PDF文件
- 📏 两种缩放模式：适合页面大小 / 适合A4纸张
- 📊 实时显示处理进度和状态
- 💾 可自定义输出文件夹和文件名后缀
- 🔄 保持原始PDF的内容质量

## 安装要求

- Python 3.7 或更高版本
- PyMuPDF 库

## 安装步骤

1. 确保已安装Python 3.7+
2. 安装依赖库：
   ```bash
   pip install -r requirements.txt
   ```
   或者直接安装：
   ```bash
   pip install PyMuPDF
   ```

## 使用方法

1. 运行程序：
   ```bash
   python pdf_fixer.py
   ```

2. 在GUI界面中：
   - 点击"选择文件"按钮选择需要修复的PDF文件（支持多选）
   - 选择输出文件夹（可选，默认与原文件相同位置）
   - 选择缩放模式：
     - **适合页面大小**：自动调整到合适的尺寸
     - **适合A4纸张**：强制缩放到A4纸张大小
   - 设置输出文件名后缀（默认为"_fixed"）
   - 点击"开始处理"按钮

3. 程序会显示处理进度，完成后会在指定位置生成修复后的PDF文件

## 技术原理

1. 使用PyMuPDF库读取原始PDF文件
2. 分析每页的尺寸信息
3. 计算合适的缩放比例（保持宽高比）
4. 创建新的PDF页面并应用缩放变换
5. 保存优化后的PDF文件

## 注意事项

- 处理大文件时可能需要较长时间，请耐心等待
- 建议在处理前备份原始文件
- 如果遇到特殊格式的PDF文件无法处理，请检查文件是否损坏

## 故障排除

### 常见问题

1. **提示缺少PyMuPDF库**
   - 解决方案：运行 `pip install PyMuPDF`

2. **处理某些文件时失败**
   - 可能原因：PDF文件损坏或格式特殊
   - 解决方案：尝试用其他PDF工具先修复文件

3. **处理后的文件仍然有问题**
   - 尝试选择不同的缩放模式
   - 检查原始文件是否本身就有问题

## 开发信息

- 开发语言：Python
- GUI框架：Tkinter
- PDF处理：PyMuPDF (fitz)
- 支持平台：Windows, macOS, Linux

## 许可证

本项目仅供学习和个人使用。
